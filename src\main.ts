import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { AppModule } from './app/app.module';
import { KeycloakService } from 'keycloak-angular';
import { KeycloakInitService } from './app/keycloak-init.service';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';

import { importProvidersFrom } from '@angular/core';
import { BrowserModule, bootstrapApplication } from '@angular/platform-browser';

const keycloakService = new KeycloakService();

const keycloakInit = new KeycloakInitService(keycloakService);

keycloakInit.init().then(() => {
  bootstrapApplication(AppModule, {
    providers: [
      provideHttpClient(withInterceptorsFromDi()),
      importProvidersFrom(BrowserModule)
    ]
  }).catch(err => console.error(err));
});
