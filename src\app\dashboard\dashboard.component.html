<!doctype html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Flexy Free Bootstrap Admin Template by WrapPixel</title>
  
</head>

<body>
  <!--  Body Wrapper -->
  <div class="page-wrapper" id="main-wrapper" data-layout="vertical" data-navbarbg="skin6" data-sidebartype="full"
    data-sidebar-position="fixed" data-header-position="fixed">

    <!--  App Topstrip -->
    
    <!-- Sidebar Start -->
  <aside class="left-sidebar">
  <!-- Sidebar scroll-->
  <div>
    <div>
      <a href="./index.html">
      <img src="assets/images/logos/esprit.png" alt="" style="width: 180px; height: auto; display: block; margin-left: 40px;" />

      </a>
      <div class="close-btn d-xl-none d-block sidebartoggler cursor-pointer" id="sidebarCollapse">
        <i class="ti ti-x fs-6"></i>
      </div>
    </div>

    <!-- Sidebar navigation-->
    <nav class="sidebar-nav scroll-sidebar" data-simplebar="">
      <ul id="sidebarnav">
        <li class="nav-small-cap">
          <span class="hide-menu">Home</span>
        </li>

        <li class="sidebar-item">
          <a class="sidebar-link" href="./index.html" aria-expanded="false">
            <i class="ti ti-atom"></i>
            <span class="hide-menu">Dashboard</span>
          </a>
        </li>

        <li class="sidebar-item">
          <a class="sidebar-link" href="./index.html" aria-expanded="false">
            <i class="ti ti-atom"></i>
            <span class="hide-menu">Rectification des Notes</span>
          </a>
        </li>

        <li class="sidebar-item">
          <a class="sidebar-link" href="./index.html" aria-expanded="false">
            <i class="ti ti-atom"></i>
            <span class="hide-menu">Planification Conseils</span>
          </a>
        </li>

        <li class="sidebar-item">
          <a class="sidebar-link" href="./index.html" aria-expanded="false">
            <i class="ti ti-atom"></i>
            <span class="hide-menu">Gestions rapports</span>
          </a>
        </li>

        <li class="sidebar-item">
          <a class="sidebar-link" href="./index.html" aria-expanded="false">
            <i class="ti ti-atom"></i>
            <span class="hide-menu">Gestion Conseil</span>
          </a>
        </li>

        <li class="sidebar-item">
          <a class="sidebar-link" href="./index.html" aria-expanded="false">
            <i class="ti ti-atom"></i>
            <span class="hide-menu">Statistique</span>
          </a>
        </li>
      </ul>
    </nav>
    <!-- End Sidebar navigation -->

  </div>
  <!-- End Sidebar scroll-->
</aside>

    <!--  Sidebar End -->
    <!--  Main wrapper -->
    <div class="body-wrapper">
      <!--  Header Start -->
      <header class="app-header">

      </header>
      <!--  Header End -->
      <div class="body-wrapper-inner">
        <div class="container-fluid">
                <div class="welcome-header">
  <h1>Bienvenue dans votre espace</h1>
  <p>Gérez efficacement vos activités académiques depuis ce tableau de bord</p>

</div>
<!-- Bouton pour ouvrir la modale -->
<
<div class="header-container">
  <div class="header-text">
    <h2>Gestion des Utilisateurs</h2>
    <p>Module 1 - Gestion des profils et authentification</p>
  </div>
<button class="add-user-btn" (click)="openModal()">
  <span class="icon">👤</span> Nouvel utilisateur
</button>
</div>


<!-- MODAL -->
<div class="modal" [ngClass]="{'show': isModalOpen}" (click)="closeOnOutsideClick($event)">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <span class="close" (click)="closeModal()">&times;</span>
    <h2>Ajouter un nouvel utilisateur</h2>

   <form (ngSubmit)="onRegister()" #userForm="ngForm" novalidate>

      
  <input type="email" name="email" [(ngModel)]="newUser.email" placeholder="Email" required>
  <div *ngIf="signupErrors.email" style="color:red">{{ signupErrors.email }}</div>

  <input type="text" name="username" [(ngModel)]="newUser.username" placeholder="Nom utilisateur" required>
  <div *ngIf="signupErrors.username" style="color:red">{{ signupErrors.username }}</div>


  <input type="password" name="password" [(ngModel)]="newUser.password" placeholder="Mot de passe" required>
  <div *ngIf="signupErrors.password" style="color:red">{{ signupErrors.password }}</div>

  

  <select name="Secteur" [(ngModel)]="newUser.Secteur" required>
    <option value="" disabled selected>Choisir un secteur</option>
    <option *ngFor="let sec of secteurs" [value]="sec">{{ sec }}</option>
  </select>
  <div *ngIf="signupErrors.Secteur" style="color:red">{{ signupErrors.Secteur }}</div>

  <!-- Role en liste déroulante -->
  <select name="role" [(ngModel)]="newUser.role" required>
    <option value="" disabled selected>Choisir un rôle</option>
    <option *ngFor="let role of roles" [value]="role">{{ role }}</option>
  </select>
  <div *ngIf="signupErrors.role" style="color:red">{{ signupErrors.role }}</div>

  <button type="submit">Enregistrer</button>
</form>

  </div>
</div>




          <!--  Row 1 -->
          <div class="row">
           
            
            <div class="col-12">
              <div class="card">
                <div class="card-body">
                  <div class="d-md-flex align-items-center">
                    <div>
                      <h4 class="card-title">Products Performance</h4>
                      <p class="card-subtitle">
                        Ample Admin Vs Pixel Admin
                      </p>
                    </div>
                    <div class="ms-auto mt-3 mt-md-0">
                      <select class="form-select theme-select border-0" aria-label="Default select example">
                        <option value="1">March 2025</option>
                        <option value="2">March 2025</option>
                        <option value="3">March 2025</option>
                      </select>
                    </div>
                  </div>
                  <div class="table-responsive mt-4">
                    <table class="table mb-0 text-nowrap varient-table align-middle fs-3">
                      <thead>
                        <tr>
                          <th scope="col" class="px-0 text-muted">
                            Assigned
                          </th>
                          <th scope="col" class="px-0 text-muted">Name</th>
                          <th scope="col" class="px-0 text-muted">
                            Priority
                          </th>
                          <th scope="col" class="px-0 text-muted text-end">
                            Budget
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td class="px-0">
                            <div class="d-flex align-items-center">
                              <img src="./assets/images/profile/user-3.jpg" class="rounded-circle" width="40"
                                alt="flexy" />
                              <div class="ms-3">
                                <h6 class="mb-0 fw-bolder">Sunil Joshi</h6>
                                <span class="text-muted">Web Designer</span>
                              </div>
                            </div>
                          </td>
                          <td class="px-0">Elite Admin</td>
                          <td class="px-0">
                            <span class="badge bg-info">Low</span>
                          </td>
                          <td class="px-0 text-dark fw-medium text-end">
                            $3.9K
                          </td>
                        </tr>
                        <tr>
                          <td class="px-0">
                            <div class="d-flex align-items-center">
                              <img src="./assets/images/profile/user-5.jpg" class="rounded-circle" width="40"
                                alt="flexy" />
                              <div class="ms-3">
                                <h6 class="mb-0 fw-bolder">
                                  Andrew McDownland
                                </h6>
                                <span class="text-muted">Project Manager</span>
                              </div>
                            </div>
                          </td>
                          <td class="px-0">Real Homes WP Theme</td>
                          <td class="px-0">
                            <span class="badge text-bg-primary">Medium</span>
                          </td>
                          <td class="px-0 text-dark fw-medium text-end">
                            $24.5K
                          </td>
                        </tr>
                        <tr>
                          <td class="px-0">
                            <div class="d-flex align-items-center">
                              <img src="./assets/images/profile/user-6.jpg" class="rounded-circle" width="40"
                                alt="flexy" />
                              <div class="ms-3">
                                <h6 class="mb-0 fw-bolder">
                                  Christopher Jamil
                                </h6>
                                <span class="text-muted">SEO Manager</span>
                              </div>
                            </div>
                          </td>
                          <td class="px-0">MedicalPro WP Theme</td>
                          <td class="px-0">
                            <span class="badge bg-warning">Hight</span>
                          </td>
                          <td class="px-0 text-dark fw-medium text-end">
                            $12.8K
                          </td>
                        </tr>
                        <tr>
                          <td class="px-0">
                            <div class="d-flex align-items-center">
                              <img src="./assets/images/profile/user-7.jpg" class="rounded-circle" width="40"
                                alt="flexy" />
                              <div class="ms-3">
                                <h6 class="mb-0 fw-bolder">Nirav Joshi</h6>
                                <span class="text-muted">Frontend Engineer</span>
                              </div>
                            </div>
                          </td>
                          <td class="px-0">Hosting Press HTML</td>
                          <td class="px-0">
                            <span class="badge bg-danger">Low</span>
                          </td>
                          <td class="px-0 text-dark fw-medium text-end">
                            $2.4K
                          </td>
                        </tr>
                        <tr>
                          <td class="px-0">
                            <div class="d-flex align-items-center">
                              <img src="./assets/images/profile/user-8.jpg" class="rounded-circle" width="40"
                                alt="flexy" />
                              <div class="ms-3">
                                <h6 class="mb-0 fw-bolder">Micheal Doe</h6>
                                <span class="text-muted">Content Writer</span>
                              </div>
                            </div>
                          </td>
                          <td class="px-0">Helping Hands WP Theme</td>
                          <td class="px-0">
                            <span class="badge bg-success">Low</span>
                          </td>
                          <td class="px-0 text-dark fw-medium text-end">
                            $9.3K
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-lg-6">
              <!-- Card -->
              <div class="card">
                <div class="card-body">
                  <h4 class="card-title mb-0">Recent Comments</h4>
                </div>
                <div class="comment-widgets scrollable mb-2 common-widget" style="height: 465px" data-simplebar="">
                  <!-- Comment Row -->
                  <div class="d-flex flex-row comment-row border-bottom p-3 gap-3">
                    <div>
                      <span><img src="./assets/images/profile/user-3.jpg" class="rounded-circle" alt="user"
                          width="50" /></span>
                    </div>
                    <div class="comment-text w-100">
                      <h6 class="fw-medium">James Anderson</h6>
                      <p class="mb-1 fs-2 text-muted">
                        Lorem Ipsum is simply dummy text of the printing and
                        type etting industry
                      </p>
                      <div class="comment-footer mt-2">
                        <div class="d-flex align-items-center">
                          <span class="
                              badge
                              bg-info-subtle
                              text-info
                              
                            ">Pending</span>
                          <span class="action-icons">
                            <a href="javascript:void(0)" class="ps-3"><i class="ti ti-edit fs-5"></i></a>
                            <a href="javascript:void(0)" class="ps-3"><i class="ti ti-check fs-5"></i></a>
                            <a href="javascript:void(0)" class="ps-3"><i class="ti ti-heart fs-5"></i></a>
                          </span>
                        </div>
                        <span class="
                            text-muted
                            ms-auto
                            fw-normal
                            fs-2
                            d-block
                            mt-2
                            text-end
                          ">April 14, 2025</span>
                      </div>
                    </div>
                  </div>
                  <!-- Comment Row -->
                  <div class="d-flex flex-row comment-row border-bottom active p-3 gap-3">
                    <div>
                      <span><img src="./assets/images/profile/user-5.jpg" class="rounded-circle" alt="user"
                          width="50" /></span>
                    </div>
                    <div class="comment-text active w-100">
                      <h6 class="fw-medium">Michael Jorden</h6>
                      <p class="mb-1 fs-2 text-muted">
                        Lorem Ipsum is simply dummy text of the printing and
                        type setting industry.
                      </p>
                      <div class="comment-footer mt-2">
                        <div class="d-flex align-items-center">
                          <span class="
                              badge
                              bg-success-subtle
                              text-success
                              
                            ">Approved</span>
                          <span class="action-icons active">
                            <a href="javascript:void(0)" class="ps-3"><i class="ti ti-edit fs-5"></i></a>
                            <a href="javascript:void(0)" class="ps-3"><i class="ti ti-circle-x fs-5"></i></a>
                            <a href="javascript:void(0)" class="ps-3"><i class="ti ti-heart text-danger fs-5"></i></a>
                          </span>
                        </div>
                        <span class="
                            text-muted
                            ms-auto
                            fw-normal
                            fs-2
                            text-end
                            mt-2
                            d-block
                          ">April 14, 2025</span>
                      </div>
                    </div>
                  </div>
                  <!-- Comment Row -->
                  <div class="d-flex flex-row comment-row border-bottom p-3 gap-3">
                    <div>
                      <span><img src="./assets/images/profile/user-6.jpg" class="rounded-circle" alt="user"
                          width="50" /></span>
                    </div>
                    <div class="comment-text w-100">
                      <h6 class="fw-medium">Johnathan Doeting</h6>
                      <p class="mb-1 fs-2 text-muted">
                        Lorem Ipsum is simply dummy text of the printing and
                        type setting industry.
                      </p>
                      <div class="comment-footer mt-2">
                        <div class="d-flex align-items-center">
                          <span class="
                              badge
                              bg-danger-subtle
                              text-danger
                              
                            ">Rejected</span>
                          <span class="action-icons">
                            <a href="javascript:void(0)" class="ps-3"><i class="ti ti-edit fs-5"></i></a>
                            <a href="javascript:void(0)" class="ps-3"><i class="ti ti-check fs-5"></i></a>
                            <a href="javascript:void(0)" class="ps-3"><i class="ti ti-heart fs-5"></i></a>
                          </span>
                        </div>
                        <span class="
                            text-muted
                            ms-auto
                            fw-normal
                            fs-2
                            d-block
                            mt-2
                            text-end
                          ">April 14, 2025</span>
                      </div>
                    </div>
                  </div>
                  <!-- Comment Row -->
                  <div class="d-flex flex-row comment-row p-3 gap-3">
                    <div>
                      <span><img src="./assets/images/profile/user-4.jpg" class="rounded-circle" alt="user"
                          width="50" /></span>
                    </div>
                    <div class="comment-text w-100">
                      <h6 class="fw-medium">James Anderson</h6>
                      <p class="mb-1 fs-2 text-muted">
                        Lorem Ipsum is simply dummy text of the printing and
                        type setting industry.
                      </p>
                      <div class="comment-footer mt-2">
                        <div class="d-flex align-items-center">
                          <span class="
                              badge
                              bg-info-subtle
                              text-info
                              
                            ">Pending</span>
                          <span class="action-icons">
                            <a href="javascript:void(0)" class="ps-3"><i class="ti ti-edit fs-5"></i></a>
                            <a href="javascript:void(0)" class="ps-3"><i class="ti ti-check fs-5"></i></a>
                            <a href="javascript:void(0)" class="ps-3"><i class="ti ti-heart fs-5"></i></a>
                          </span>
                        </div>
                        <span class="
                            text-muted
                            ms-auto
                            fw-normal
                            fs-2
                            d-block
                            text-end
                            mt-2
                          ">April 14, 2025</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-lg-6">
              <div class="card">
                <div class="card-body">
                  <div class="d-flex align-items-center">
                    <h4 class="card-title mb-0">Weather Report</h4>
                    <select class="form-select w-auto ms-auto">
                      <option selected="">Today</option>
                      <option value="1">Weekly</option>
                    </select>
                  </div>
                  <div class="d-flex align-items-center flex-row mt-4">
                    <div class="p-2 display-5 text-primary">
                      <i class="ti ti-cloud-snow"></i>
                      <span>73<sup>°</sup></span>
                    </div>
                    <div class="p-2">
                      <h3 class="mb-0">Saturday</h3>
                      <small>Ahmedabad, India</small>
                    </div>
                  </div>
                  <table class="table table-borderless">
                    <tbody>
                      <tr>
                        <td>Wind</td>
                        <td class="fw-medium">ESE 17 mph</td>
                      </tr>
                      <tr>
                        <td>Humidity</td>
                        <td class="fw-medium">83%</td>
                      </tr>
                      <tr>
                        <td>Pressure</td>
                        <td class="fw-medium">28.56 in</td>
                      </tr>
                      <tr>
                        <td>Cloud Cover</td>
                        <td class="fw-medium">78%</td>
                      </tr>
                      <tr>
                        <td>Ceiling</td>
                        <td class="fw-medium">25760 ft</td>
                      </tr>
                    </tbody>
                  </table>
                  <hr />
                  <ul class="list-unstyled row text-center city-weather-days mb-0">
                    <li class="col">
                      <i class="ti ti-sun-high fs-4"></i><span>09:30</span>
                      <h3 class="mb-0 fs-6 lh-base">70<sup>°</sup></h3>
                    </li>
                    <li class="col">
                      <i class="ti ti-cloud fs-4"></i><span>11:30</span>
                      <h3 class="mb-0 fs-6 lh-base">72<sup>°</sup></h3>
                    </li>
                    <li class="col">
                      <i class="ti ti-cloud-rain fs-4"></i><span>13:30</span>
                      <h3 class="mb-0 fs-6 lh-base">75<sup>°</sup></h3>
                    </li>
                    <li class="col">
                      <i class="ti ti-cloud-snow fs-4"></i><span>15:30</span>
                      <h3 class="mb-0 fs-6 lh-base">76<sup>°</sup></h3>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          <div class="py-6 px-6 text-center">
            <p class="mb-0 fs-4">Design and Developed by <a href="#"
                class="pe-1 text-primary text-decoration-underline">Wrappixel.com</a> Distributed by <a href="https://themewagon.com" target="_blank" >ThemeWagon</a></p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script src="./assets/libs/jquery/dist/jquery.min.js"></script>
  <script src="./assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
  <script src="./assets/js/sidebarmenu.js"></script>
  <script src="./assets/js/app.min.js"></script>
  <script src="./assets/libs/apexcharts/dist/apexcharts.min.js"></script>
  <script src="./assets/libs/simplebar/dist/simplebar.js"></script>
  <script src="./assets/js/dashboard.js"></script>
  <!-- solar icons -->
  <script src="https://cdn.jsdelivr.net/npm/iconify-icon@1.0.8/dist/iconify-icon.min.js"></script>
</body>

</html>