import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { DashboardComponent } from './dashboard/dashboard.component';
import { UtilisateurComponent } from './utilisateur/utilisateur.component';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { FormsModule } from '@angular/forms';
import { MotpasseComponent } from './motpasse/motpasse.component';
import { ResetPasswordComponent } from './reset-password/reset-password.component';
import { WebcamModule } from 'ngx-webcam';
import { RectificationComponent } from './rectification/rectification.component';
import { KeycloakAngularModule, KeycloakService } from 'keycloak-angular';
import { KeycloakInitService } from './keycloak-init.service';
import { KeycloakTokenInterceptor } from './keycloak.interceptor';
import { HTTP_INTERCEPTORS } from '@angular/common/http';

@NgModule({ declarations: [
        AppComponent,
        DashboardComponent,
        UtilisateurComponent,
        MotpasseComponent,
        ResetPasswordComponent,
        RectificationComponent
    ],
    bootstrap: [AppComponent], imports: [BrowserModule,
        AppRoutingModule,
        FormsModule,
        WebcamModule,
        KeycloakAngularModule], providers: [
        {
            provide: HTTP_INTERCEPTORS,
            useClass: KeycloakTokenInterceptor,
            multi: true
        },
        provideHttpClient(withInterceptorsFromDi())
    ] })
export class AppModule { }
