import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

export interface Rectification {
  id?: number;
  etudiantNom: string;
  classe: string;
  option: string;
  ancienneNote: number;
  nouvelleNote: number;
  justification: string;
  status?: string;
  dateDemande?: string;
}

@Injectable({ providedIn: 'root' })
export class RectificationService {
  private baseUrl = 'http://localhost:8085/api/rectification';

  constructor(private http: HttpClient) {}

  create(data: Rectification): Observable<Rectification> {
    return this.http.post<Rectification>(this.baseUrl, data);
  }

  getAll(): Observable<Rectification[]> {
    return this.http.get<Rectification[]>(this.baseUrl);
  }

  updateStatus(id: number, status: string): Observable<Rectification> {
    return this.http.put<Rectification>(`${this.baseUrl}/${id}/status?status=${status}`, {});
  }
}
